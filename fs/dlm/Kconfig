# SPDX-License-Identifier: GPL-2.0-only
menuconfig DLM
	tristate "Distributed Lock Manager (DLM)"
	depends on INET
	depends on SYSFS && CONFIGFS_FS && (IPV6 || IPV6=n)
	select IP_SCTP
	help
	A general purpose distributed lock manager for kernel or userspace
	applications.

config DLM_DEBUG
	bool "DLM debugging"
	depends on DLM
	help
	Under the debugfs mount point, the name of each lockspace will
	appear as a file in the "dlm" directory.  The output is the
	list of resource and locks the local node knows about.
