# SPDX-License-Identifier: GPL-2.0-only
config JFS_FS
	tristate "JFS filesystem support"
	select B<PERSON><PERSON>ER_HEAD
	select NLS
	select NLS_UCS2_UTILS
	select CRC32
	select LEGACY_DIRECT_IO
	help
	  This is a port of IBM's Journaled Filesystem .  More information is
	  available in the file <file:Documentation/admin-guide/jfs.rst>.

	  If you do not intend to use the JFS filesystem, say N.

config JFS_POSIX_ACL
	bool "JFS POSIX Access Control Lists"
	depends on JFS_FS
	select FS_POSIX_ACL
	help
	  Posix Access Control Lists (ACLs) support permissions for users and
	  groups beyond the owner/group/world scheme.

	  If you don't know what Access Control Lists are, say N

config JFS_SECURITY
	bool "JFS Security Labels"
	depends on JFS_FS
	help
	  Security labels support alternative access control models
	  implemented by security modules like SELinux.  This option
	  enables an extended attribute handler for file security
	  labels in the jfs filesystem.

	  If you are not using a security module that requires using
	  extended attributes for file security labels, say N.

config JFS_DEBUG
	bool "JFS debugging"
	depends on JFS_FS
	help
	  If you are experiencing any problems with the JFS filesystem, say
	  Y here.  This will result in additional debugging messages to be
	  written to the system log.  Under normal circumstances, this
	  results in very little overhead.

config JFS_STATISTICS
	bool "JFS statistics"
	depends on JFS_FS
	help
	  Enabling this option will cause statistics from the JFS file system
	  to be made available to the user in the /proc/fs/jfs/ directory.
