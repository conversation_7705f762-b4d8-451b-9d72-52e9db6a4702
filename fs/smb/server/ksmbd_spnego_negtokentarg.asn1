-- SPDX-License-Identifier: BSD-3-Clause
--
-- Copyright (C) 1998 IETF Trust and the persons identified as authors
-- of the code
--
-- https://www.rfc-editor.org/rfc/rfc2478#section-3.2.1

GSSAPI ::=
	CHOICE {
		negTokenInit
			[0] ANY,
		negTokenTarg
			[1] NegTokenTarg
	}

NegTokenTarg ::=
	SEQUENCE {
		negResult
			[0] ENUMERATED OPTIONAL,
		supportedMech
			[1] OBJECT IDENTIFIER OPTIONAL,
		responseToken
			[2] OCTET STRING OPTIONAL ({ksmbd_neg_token_targ_resp_token}),
		mechListMIC
			[3] OCTET STRING OPTIONAL
	}
