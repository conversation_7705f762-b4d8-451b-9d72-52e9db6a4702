# SPDX-License-Identifier: GPL-2.0-only
config NFS_FS
	tristate "NFS client support"
	depends on INET && FILE_LOCKING && MULTIUSER
	select CR<PERSON>32
	select L<PERSON><PERSON>D
	select SUNRPC
	select NFS_COMMON
	select NFS_ACL_SUPPORT if NFS_V3_ACL
	help
	  Choose Y here if you want to access files residing on other
	  computers using Sun's Network File System protocol.  To compile
	  this file system support as a module, choose M here: the module
	  will be called nfs.

	  To mount file systems exported by NFS servers, you also need to
	  install the user space mount.nfs command which can be found in
	  the Linux nfs-utils package, available from http://linux-nfs.org/.
	  Information about using the mount command is available in the
	  mount(8) man page.  More detail about the Linux NFS client
	  implementation is available via the nfs(5) man page.

	  Below you can choose which versions of the NFS protocol are
	  available in the kernel to mount NFS servers.  Support for NFS
	  version 2 (RFC 1094) is always available when NFS_FS is selected.

	  To configure a system which mounts its root file system via NFS
	  at boot time, say Y here, select "Kernel level IP
	  autoconfiguration" in the NETWORK menu, and select "Root file
	  system on NFS" below.  You cannot compile this file system as a
	  module in this case.

	  If unsure, say N.

config NFS_V2
	tristate "NFS client support for NFS version 2"
	depends on NFS_FS
	default n
	help
	  This option enables support for version 2 of the NFS protocol
	  (RFC 1094) in the kernel's NFS client.

	  If unsure, say N.

config NFS_V3
	tristate "NFS client support for NFS version 3"
	depends on NFS_FS
	default y
	help
	  This option enables support for version 3 of the NFS protocol
	  (RFC 1813) in the kernel's NFS client.

	  If unsure, say Y.

config NFS_V3_ACL
	bool "NFS client support for the NFSv3 ACL protocol extension"
	depends on NFS_V3
	help
	  Some NFS servers support an auxiliary NFSv3 ACL protocol that
	  Sun added to Solaris but never became an official part of the
	  NFS version 3 protocol.  This protocol extension allows
	  applications on NFS clients to manipulate POSIX Access Control
	  Lists on files residing on NFS servers.  NFS servers enforce
	  ACLs on local files whether this protocol is available or not.

	  Choose Y here if your NFS server supports the Solaris NFSv3 ACL
	  protocol extension and you want your NFS client to allow
	  applications to access and modify ACLs on files on the server.

	  Most NFS servers don't support the Solaris NFSv3 ACL protocol
	  extension.  You can choose N here or specify the "noacl" mount
	  option to prevent your NFS client from trying to use the NFSv3
	  ACL protocol.

	  If unsure, say N.

config NFS_V4
	tristate "NFS client support for NFS version 4"
	depends on NFS_FS
	select KEYS
	help
	  This option enables support for version 4 of the NFS protocol
	  (RFC 3530) in the kernel's NFS client.

	  To mount NFS servers using NFSv4, you also need to install user
	  space programs which can be found in the Linux nfs-utils package,
	  available from http://linux-nfs.org/.

	  If unsure, say Y.

config NFS_SWAP
	bool "Provide swap over NFS support"
	default n
	depends on NFS_FS && SWAP
	select SUNRPC_SWAP
	help
	  This option enables swapon to work on files located on NFS mounts.

config NFS_V4_1
	bool "NFS client support for NFSv4.1"
	depends on NFS_V4
	select SUNRPC_BACKCHANNEL
	help
	  This option enables support for minor version 1 of the NFSv4 protocol
	  (RFC 5661) in the kernel's NFS client.

	  If unsure, say N.

config NFS_V4_2
	bool "NFS client support for NFSv4.2"
	depends on NFS_V4_1
	help
	  This option enables support for minor version 2 of the NFSv4 protocol
	  in the kernel's NFS client.

	  If unsure, say N.

config PNFS_FILE_LAYOUT
	tristate
	depends on NFS_V4_1
	default NFS_V4

config PNFS_BLOCK
	tristate
	depends on NFS_V4_1 && BLK_DEV_DM
	default NFS_V4

config PNFS_FLEXFILE_LAYOUT
	tristate
	depends on NFS_V4_1
	default NFS_V4

config NFS_V4_1_IMPLEMENTATION_ID_DOMAIN
	string "NFSv4.1 Implementation ID Domain"
	depends on NFS_V4_1
	default "kernel.org"
	help
	  This option defines the domain portion of the implementation ID that
	  may be sent in the NFS exchange_id operation.  The value must be in
	  the format of a DNS domain name and should be set to the DNS domain
	  name of the distribution.
	  If the NFS client is unchanged from the upstream kernel, this
	  option should be set to the default "kernel.org".

config NFS_V4_1_MIGRATION
	bool "NFSv4.1 client support for migration"
	depends on NFS_V4_1
	default n
	help
	  This option makes the NFS client advertise to NFSv4.1 servers that
          it can support NFSv4 migration.

          The NFSv4.1 pieces of the Linux NFSv4 migration implementation are
          still experimental.  If you are not an NFSv4 developer, say N here.

config NFS_V4_SECURITY_LABEL
	bool
	depends on NFS_V4_2 && SECURITY
	default y

config ROOT_NFS
	bool "Root file system on NFS"
	depends on NFS_FS=y && IP_PNP
	help
	  If you want your system to mount its root file system via NFS,
	  choose Y here.  This is common practice for managing systems
	  without local permanent storage.  For details, read
	  <file:Documentation/admin-guide/nfs/nfsroot.rst>.

	  Most people say N here.

config NFS_FSCACHE
	bool "Provide NFS client caching support"
	depends on NFS_FS
	select NETFS_SUPPORT
	select FSCACHE
	help
	  Say Y here if you want NFS data to be cached locally on disc through
	  the general filesystem cache manager

config NFS_USE_LEGACY_DNS
	bool "Use the legacy NFS DNS resolver"
	depends on NFS_V4
	help
	  The kernel now provides a method for translating a host name into an
	  IP address.  Select Y here if you would rather use your own DNS
	  resolver script.

	  If unsure, say N

config NFS_USE_KERNEL_DNS
	bool
	depends on NFS_V4 && !NFS_USE_LEGACY_DNS
	select DNS_RESOLVER
	default y

config NFS_DEBUG
	bool
	depends on NFS_FS && SUNRPC_DEBUG
	default y

config NFS_DISABLE_UDP_SUPPORT
       bool "NFS: Disable NFS UDP protocol support"
       depends on NFS_FS
       default y
       help
	 Choose Y here to disable the use of NFS over UDP. NFS over UDP
	 on modern networks (1Gb+) can lead to data corruption caused by
	 fragmentation during high loads.

config NFS_V4_2_READ_PLUS
	bool "NFS: Enable support for the NFSv4.2 READ_PLUS operation"
	depends on NFS_V4_2
	default y
	help
	 Choose Y here to enable use of the NFS v4.2 READ_PLUS operation.
