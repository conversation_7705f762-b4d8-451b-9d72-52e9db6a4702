# SPDX-License-Identifier: GPL-2.0-only
config HFS_FS
	tristate "Apple Macintosh file system support"
	depends on BLOCK
	select BUFFER_HEAD
	select NLS
	select LEGACY_DIRECT_IO
	help
	  If you say Y here, you will be able to mount Macintosh-formatted
	  floppy disks and hard drive partitions with full read-write access.
	  Please read <file:Documentation/filesystems/hfs.rst> to learn about
	  the available mount options.

	  To compile this file system support as a module, choose M here: the
	  module will be called hfs.
