// SPDX-License-Identifier: GPL-2.0
/*
 *  linux/fs/ufs/file.c
 *
 * Copyright (C) 1998
 * <PERSON> <<EMAIL>>
 * Charles University, Faculty of Mathematics and Physics
 *
 *  from
 *
 *  linux/fs/ext2/file.c
 *
 * Copyright (C) 1992, 1993, 1994, 1995
 * Remy <PERSON> (<EMAIL>)
 * Laboratoire MASI - Institut Blaise Pascal
 * Universite Pierre et Marie Curie (Paris VI)
 *
 *  from
 *
 *  linux/fs/minix/file.c
 *
 *  Copyright (C) 1991, 1992  Linus Torvalds
 *
 *  ext2 fs regular file handling primitives
 */

#include <linux/fs.h>

#include "ufs_fs.h"
#include "ufs.h"

/*
 * We have mostly NULL's here: the current defaults are ok for
 * the ufs filesystem.
 */
 
const struct file_operations ufs_file_operations = {
	.llseek		= generic_file_llseek,
	.read_iter	= generic_file_read_iter,
	.write_iter	= generic_file_write_iter,
	.mmap		= generic_file_mmap,
	.open           = generic_file_open,
	.fsync		= generic_file_fsync,
	.splice_read	= filemap_splice_read,
	.splice_write	= iter_file_splice_write,
};
