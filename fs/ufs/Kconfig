# SPDX-License-Identifier: GPL-2.0-only
config UFS_FS
	tristate "UFS file system support (read only)"
	depends on BLOCK
	select BUFFER_HEAD
	help
	  BSD and derivate versions of Unix (such as SunOS, FreeBSD, NetBSD,
	  OpenBSD and NeXTstep) use a file system called UFS. Some System V
	  Unixes can create and mount hard disk partitions and diskettes using
	  this file system as well. Saying Y here will allow you to read from
	  these partitions; if you also want to write to them, say Y to the
	  experimental "UFS file system write support", below. Please read the
	  file <file:Documentation/admin-guide/ufs.rst> for more information.

          The recently released UFS2 variant (used in FreeBSD 5.x) is
          READ-ONLY supported.

	  Note that this option is generally not needed for floppies, since a
	  good portable way to transport files and directories between unixes
	  (and even other operating systems) is given by the tar program ("man
	  tar" or preferably "info tar").

	  When accessing NeXTstep files, you may need to convert them from the
	  NeXT character set to the Latin1 character set; use the program
	  recode ("info recode") for this purpose.

	  To compile the UFS file system support as a module, choose <PERSON> here: the
	  module will be called ufs.

	  If you haven't heard about all of this before, it's safe to say N.

config UFS_FS_WRITE
	bool "UFS file system write support (DANGEROUS)"
	depends on UFS_FS
	help
	  Say Y here if you want to try writing to UFS partitions. This is
	  experimental, so you should back up your UFS partitions beforehand.

config UFS_DEBUG
	bool "UFS debugging"
	depends on UFS_FS
	help
	  If you are experiencing any problems with the UFS filesystem, say
	  Y here.  This will result in _many_ additional debugging messages to be
	  written to the system log.
